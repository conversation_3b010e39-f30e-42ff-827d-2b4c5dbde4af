# 离子镀膜磁控点分布运算系统 - 使用说明

## 系统概述
本系统用于计算离子镀膜磁控点的分布，通过分析原始数据生成符合条件的磁控点组合。

## 功能特点
- 支持TXT文件导入，自动验证数据格式
- 可配置的参数设置（最大数值、磁控点位数、频次范围等）
- 多线程处理，支持大数据量计算
- 实时进度显示和状态更新
- 结果预览和导出功能
- 支持取消操作

## 使用步骤

### 1. 启动程序
运行 `CalculatorSystem.exe` 启动程序。

### 2. 导入数据文件
1. 点击"选择文件"按钮
2. 选择包含原始数据的TXT文件（如"磁控点总数据1.txt"）
3. 系统会自动验证数据格式并显示加载状态

### 3. 设置参数
在"参数设置"区域配置以下参数：

- **最大数值(33-37)**: 设置数据范围的最大值
- **磁控点位数(6/9)**: 选择生成6位或9位磁控点
- **2数组合频次范围**: 设置参与运算的2数组合出现次数范围（如5-50次）
- **最终结果频次范围**: 设置最终筛选的频次范围（如10-15次）

### 4. 开始计算
1. 点击"开始计算"按钮
2. 系统会显示处理进度和当前状态
3. 可以随时点击"取消计算"中止处理

### 5. 查看结果
- 计算完成后，结果会显示在"计算结果"区域
- 显示结果数量统计
- 可以滚动查看前100个结果（如果结果较多）

### 6. 导出结果
1. 点击"导出结果"按钮
2. 选择保存位置和文件名
3. 系统会生成包含完整结果的TXT文件

## 数据格式要求

### 输入数据格式
- 每行一个数字序列，用"-"分隔
- 数字必须按从小到大排序
- 同一行内不能有重复数字
- 数字范围：1到设定的最大值

**正确示例：**
```
1-2-7-11-14-15-25-29-30-32
6-8-9-10-14-19-20-27-30-33
3-4-7-10-14-19-24-27-31-33
```

**错误示例：**
```
1-5-7-9-10-11-11-15-28  (有重复数字11)
5-3-7-9-10-15-28        (未按升序排列)
```

### 输出结果格式
导出的TXT文件包含：
- 文件头信息（生成时间、参数设置等）
- 所有符合条件的磁控点组合
- 每行一个磁控点组合

## 算法说明

### 处理流程
1. **数据验证**: 检查输入数据格式和排序
2. **2数组合生成**: 从每行数据生成所有可能的2数组合
3. **频次统计**: 统计每个2数组合的出现次数
4. **有效组合筛选**: 根据频次范围筛选有效的2数组合
5. **磁控点生成**: 基于有效2数组合生成完整的磁控点
6. **结果筛选**: 统计磁控点频次并筛选最终结果
7. **无效数据剔除**: 移除包含无效2数组合的磁控点

### 性能优化
- 使用多线程处理避免界面卡顿
- 优化的组合算法减少内存占用
- 实时进度反馈提升用户体验

## 注意事项

1. **数据文件**: 确保数据文件格式正确，系统会进行验证
2. **参数设置**: 合理设置频次范围，避免结果过多或过少
3. **内存使用**: 大数据量计算时注意系统内存使用情况
4. **处理时间**: 复杂计算可能需要较长时间，请耐心等待
5. **结果验证**: 建议对重要结果进行人工验证

## 故障排除

### 常见问题
1. **文件加载失败**: 检查文件路径和格式
2. **数据验证错误**: 根据提示修正数据格式
3. **计算中断**: 检查系统资源，重新开始计算
4. **结果为空**: 调整参数设置，扩大筛选范围

### 技术支持
如遇到技术问题，请检查：
- 系统要求：Windows 7及以上，.NET Framework 4.7.2
- 内存要求：建议4GB以上
- 磁盘空间：确保有足够空间保存结果文件

## 版本信息
- 版本：1.0
- 开发环境：Visual Studio 2022, .NET Framework 4.7.2
- 支持系统：Windows 7/8/10/11
