namespace CalculatorSystem
{
    partial class Form1
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        private System.Windows.Forms.GroupBox groupBoxFile;
        private System.Windows.Forms.Button btnSelectFile;
        private System.Windows.Forms.TextBox txtFilePath;
        private System.Windows.Forms.Label lblFilePath;
        private System.Windows.Forms.GroupBox groupBoxSettings;
        private System.Windows.Forms.NumericUpDown numMaxNumber;
        private System.Windows.Forms.Label lblMaxNumber;
        private System.Windows.Forms.NumericUpDown numMagneticPoints;
        private System.Windows.Forms.Label lblMagneticPoints;
        private System.Windows.Forms.NumericUpDown numTwoCombinationMax;
        private System.Windows.Forms.NumericUpDown numTwoCombinationMin;
        private System.Windows.Forms.Label lblTwoCombination;
        private System.Windows.Forms.NumericUpDown numResultMax;
        private System.Windows.Forms.NumericUpDown numResultMin;
        private System.Windows.Forms.Label lblResultRange;
        private System.Windows.Forms.GroupBox groupBoxProcess;
        private System.Windows.Forms.Button btnStart;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.ProgressBar progressBar;
        private System.Windows.Forms.Label lblStatus;
        private System.Windows.Forms.GroupBox groupBoxResults;
        private System.Windows.Forms.TextBox txtResults;
        private System.Windows.Forms.Label lblResultCount;
        private System.Windows.Forms.Button btnExport;
        private System.Windows.Forms.OpenFileDialog openFileDialog;
        private System.Windows.Forms.SaveFileDialog saveFileDialog;

        /// <summary>
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.groupBoxFile = new System.Windows.Forms.GroupBox();
            this.btnSelectFile = new System.Windows.Forms.Button();
            this.txtFilePath = new System.Windows.Forms.TextBox();
            this.lblFilePath = new System.Windows.Forms.Label();
            this.groupBoxSettings = new System.Windows.Forms.GroupBox();
            this.numMaxNumber = new System.Windows.Forms.NumericUpDown();
            this.lblMaxNumber = new System.Windows.Forms.Label();
            this.numMagneticPoints = new System.Windows.Forms.NumericUpDown();
            this.lblMagneticPoints = new System.Windows.Forms.Label();
            this.numTwoCombinationMax = new System.Windows.Forms.NumericUpDown();
            this.numTwoCombinationMin = new System.Windows.Forms.NumericUpDown();
            this.lblTwoCombination = new System.Windows.Forms.Label();
            this.numResultMax = new System.Windows.Forms.NumericUpDown();
            this.numResultMin = new System.Windows.Forms.NumericUpDown();
            this.lblResultRange = new System.Windows.Forms.Label();
            this.groupBoxProcess = new System.Windows.Forms.GroupBox();
            this.btnStart = new System.Windows.Forms.Button();
            this.btnCancel = new System.Windows.Forms.Button();
            this.progressBar = new System.Windows.Forms.ProgressBar();
            this.lblStatus = new System.Windows.Forms.Label();
            this.groupBoxResults = new System.Windows.Forms.GroupBox();
            this.txtResults = new System.Windows.Forms.TextBox();
            this.lblResultCount = new System.Windows.Forms.Label();
            this.btnExport = new System.Windows.Forms.Button();
            this.openFileDialog = new System.Windows.Forms.OpenFileDialog();
            this.saveFileDialog = new System.Windows.Forms.SaveFileDialog();
            this.groupBoxFile.SuspendLayout();
            this.groupBoxSettings.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxNumber)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMagneticPoints)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numTwoCombinationMax)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numTwoCombinationMin)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numResultMax)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numResultMin)).BeginInit();
            this.groupBoxProcess.SuspendLayout();
            this.groupBoxResults.SuspendLayout();
            this.SuspendLayout();
            //
            // groupBoxFile
            //
            this.groupBoxFile.Controls.Add(this.btnSelectFile);
            this.groupBoxFile.Controls.Add(this.txtFilePath);
            this.groupBoxFile.Controls.Add(this.lblFilePath);
            this.groupBoxFile.Location = new System.Drawing.Point(12, 12);
            this.groupBoxFile.Name = "groupBoxFile";
            this.groupBoxFile.Size = new System.Drawing.Size(760, 80);
            this.groupBoxFile.TabIndex = 0;
            this.groupBoxFile.TabStop = false;
            this.groupBoxFile.Text = "数据文件";
            //
            // btnSelectFile
            //
            this.btnSelectFile.Location = new System.Drawing.Point(650, 35);
            this.btnSelectFile.Name = "btnSelectFile";
            this.btnSelectFile.Size = new System.Drawing.Size(90, 25);
            this.btnSelectFile.TabIndex = 2;
            this.btnSelectFile.Text = "选择文件";
            this.btnSelectFile.UseVisualStyleBackColor = true;
            this.btnSelectFile.Click += new System.EventHandler(this.btnSelectFile_Click);
            //
            // txtFilePath
            //
            this.txtFilePath.Location = new System.Drawing.Point(80, 37);
            this.txtFilePath.Name = "txtFilePath";
            this.txtFilePath.ReadOnly = true;
            this.txtFilePath.Size = new System.Drawing.Size(560, 21);
            this.txtFilePath.TabIndex = 1;
            //
            // lblFilePath
            //
            this.lblFilePath.AutoSize = true;
            this.lblFilePath.Location = new System.Drawing.Point(15, 40);
            this.lblFilePath.Name = "lblFilePath";
            this.lblFilePath.Size = new System.Drawing.Size(59, 12);
            this.lblFilePath.TabIndex = 0;
            this.lblFilePath.Text = "文件路径:";
            //
            // groupBoxSettings
            //
            this.groupBoxSettings.Controls.Add(this.numMaxNumber);
            this.groupBoxSettings.Controls.Add(this.lblMaxNumber);
            this.groupBoxSettings.Controls.Add(this.numMagneticPoints);
            this.groupBoxSettings.Controls.Add(this.lblMagneticPoints);
            this.groupBoxSettings.Controls.Add(this.numTwoCombinationMax);
            this.groupBoxSettings.Controls.Add(this.numTwoCombinationMin);
            this.groupBoxSettings.Controls.Add(this.lblTwoCombination);
            this.groupBoxSettings.Controls.Add(this.numResultMax);
            this.groupBoxSettings.Controls.Add(this.numResultMin);
            this.groupBoxSettings.Controls.Add(this.lblResultRange);
            this.groupBoxSettings.Location = new System.Drawing.Point(12, 98);
            this.groupBoxSettings.Name = "groupBoxSettings";
            this.groupBoxSettings.Size = new System.Drawing.Size(760, 120);
            this.groupBoxSettings.TabIndex = 1;
            this.groupBoxSettings.TabStop = false;
            this.groupBoxSettings.Text = "参数设置";
            //
            // numMaxNumber
            //
            this.numMaxNumber.Location = new System.Drawing.Point(120, 25);
            this.numMaxNumber.Maximum = new decimal(new int[] {
            37,
            0,
            0,
            0});
            this.numMaxNumber.Minimum = new decimal(new int[] {
            33,
            0,
            0,
            0});
            this.numMaxNumber.Name = "numMaxNumber";
            this.numMaxNumber.Size = new System.Drawing.Size(80, 21);
            this.numMaxNumber.TabIndex = 1;
            this.numMaxNumber.Value = new decimal(new int[] {
            33,
            0,
            0,
            0});
            //
            // lblMaxNumber
            //
            this.lblMaxNumber.AutoSize = true;
            this.lblMaxNumber.Location = new System.Drawing.Point(15, 27);
            this.lblMaxNumber.Name = "lblMaxNumber";
            this.lblMaxNumber.Size = new System.Drawing.Size(95, 12);
            this.lblMaxNumber.TabIndex = 0;
            this.lblMaxNumber.Text = "最大数值(33-37):";
            //
            // numMagneticPoints
            //
            this.numMagneticPoints.Location = new System.Drawing.Point(350, 25);
            this.numMagneticPoints.Maximum = new decimal(new int[] {
            9,
            0,
            0,
            0});
            this.numMagneticPoints.Minimum = new decimal(new int[] {
            6,
            0,
            0,
            0});
            this.numMagneticPoints.Name = "numMagneticPoints";
            this.numMagneticPoints.Size = new System.Drawing.Size(80, 21);
            this.numMagneticPoints.TabIndex = 3;
            this.numMagneticPoints.Value = new decimal(new int[] {
            6,
            0,
            0,
            0});
            //
            // lblMagneticPoints
            //
            this.lblMagneticPoints.AutoSize = true;
            this.lblMagneticPoints.Location = new System.Drawing.Point(250, 27);
            this.lblMagneticPoints.Name = "lblMagneticPoints";
            this.lblMagneticPoints.Size = new System.Drawing.Size(89, 12);
            this.lblMagneticPoints.TabIndex = 2;
            this.lblMagneticPoints.Text = "磁控点位数(6/9):";
            //
            // numTwoCombinationMax
            //
            this.numTwoCombinationMax.Location = new System.Drawing.Point(200, 60);
            this.numTwoCombinationMax.Maximum = new decimal(new int[] {
            1000,
            0,
            0,
            0});
            this.numTwoCombinationMax.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numTwoCombinationMax.Name = "numTwoCombinationMax";
            this.numTwoCombinationMax.Size = new System.Drawing.Size(60, 21);
            this.numTwoCombinationMax.TabIndex = 6;
            this.numTwoCombinationMax.Value = new decimal(new int[] {
            50,
            0,
            0,
            0});
            //
            // numTwoCombinationMin
            //
            this.numTwoCombinationMin.Location = new System.Drawing.Point(120, 60);
            this.numTwoCombinationMin.Maximum = new decimal(new int[] {
            1000,
            0,
            0,
            0});
            this.numTwoCombinationMin.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numTwoCombinationMin.Name = "numTwoCombinationMin";
            this.numTwoCombinationMin.Size = new System.Drawing.Size(60, 21);
            this.numTwoCombinationMin.TabIndex = 5;
            this.numTwoCombinationMin.Value = new decimal(new int[] {
            5,
            0,
            0,
            0});
            //
            // lblTwoCombination
            //
            this.lblTwoCombination.AutoSize = true;
            this.lblTwoCombination.Location = new System.Drawing.Point(15, 62);
            this.lblTwoCombination.Name = "lblTwoCombination";
            this.lblTwoCombination.Size = new System.Drawing.Size(101, 12);
            this.lblTwoCombination.TabIndex = 4;
            this.lblTwoCombination.Text = "2数组合频次范围:";
            //
            // numResultMax
            //
            this.numResultMax.Location = new System.Drawing.Point(550, 60);
            this.numResultMax.Maximum = new decimal(new int[] {
            1000,
            0,
            0,
            0});
            this.numResultMax.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numResultMax.Name = "numResultMax";
            this.numResultMax.Size = new System.Drawing.Size(60, 21);
            this.numResultMax.TabIndex = 9;
            this.numResultMax.Value = new decimal(new int[] {
            15,
            0,
            0,
            0});
            //
            // numResultMin
            //
            this.numResultMin.Location = new System.Drawing.Point(470, 60);
            this.numResultMin.Maximum = new decimal(new int[] {
            1000,
            0,
            0,
            0});
            this.numResultMin.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numResultMin.Name = "numResultMin";
            this.numResultMin.Size = new System.Drawing.Size(60, 21);
            this.numResultMin.TabIndex = 8;
            this.numResultMin.Value = new decimal(new int[] {
            10,
            0,
            0,
            0});
            //
            // lblResultRange
            //
            this.lblResultRange.AutoSize = true;
            this.lblResultRange.Location = new System.Drawing.Point(350, 62);
            this.lblResultRange.Name = "lblResultRange";
            this.lblResultRange.Size = new System.Drawing.Size(113, 12);
            this.lblResultRange.TabIndex = 7;
            this.lblResultRange.Text = "最终结果频次范围:";
            //
            // groupBoxProcess
            //
            this.groupBoxProcess.Controls.Add(this.btnStart);
            this.groupBoxProcess.Controls.Add(this.btnCancel);
            this.groupBoxProcess.Controls.Add(this.progressBar);
            this.groupBoxProcess.Controls.Add(this.lblStatus);
            this.groupBoxProcess.Location = new System.Drawing.Point(12, 224);
            this.groupBoxProcess.Name = "groupBoxProcess";
            this.groupBoxProcess.Size = new System.Drawing.Size(760, 100);
            this.groupBoxProcess.TabIndex = 2;
            this.groupBoxProcess.TabStop = false;
            this.groupBoxProcess.Text = "处理进度";
            //
            // btnStart
            //
            this.btnStart.Location = new System.Drawing.Point(15, 60);
            this.btnStart.Name = "btnStart";
            this.btnStart.Size = new System.Drawing.Size(100, 30);
            this.btnStart.TabIndex = 0;
            this.btnStart.Text = "开始计算";
            this.btnStart.UseVisualStyleBackColor = true;
            this.btnStart.Click += new System.EventHandler(this.btnStart_Click);
            //
            // btnCancel
            //
            this.btnCancel.Enabled = false;
            this.btnCancel.Location = new System.Drawing.Point(130, 60);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(100, 30);
            this.btnCancel.TabIndex = 1;
            this.btnCancel.Text = "取消计算";
            this.btnCancel.UseVisualStyleBackColor = true;
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            //
            // progressBar
            //
            this.progressBar.Location = new System.Drawing.Point(15, 25);
            this.progressBar.Name = "progressBar";
            this.progressBar.Size = new System.Drawing.Size(725, 20);
            this.progressBar.TabIndex = 2;
            //
            // lblStatus
            //
            this.lblStatus.AutoSize = true;
            this.lblStatus.Location = new System.Drawing.Point(250, 70);
            this.lblStatus.Name = "lblStatus";
            this.lblStatus.Size = new System.Drawing.Size(53, 12);
            this.lblStatus.TabIndex = 3;
            this.lblStatus.Text = "准备就绪";
            //
            // groupBoxResults
            //
            this.groupBoxResults.Controls.Add(this.txtResults);
            this.groupBoxResults.Controls.Add(this.lblResultCount);
            this.groupBoxResults.Controls.Add(this.btnExport);
            this.groupBoxResults.Location = new System.Drawing.Point(12, 330);
            this.groupBoxResults.Name = "groupBoxResults";
            this.groupBoxResults.Size = new System.Drawing.Size(760, 250);
            this.groupBoxResults.TabIndex = 3;
            this.groupBoxResults.TabStop = false;
            this.groupBoxResults.Text = "计算结果";
            //
            // txtResults
            //
            this.txtResults.Font = new System.Drawing.Font("Consolas", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtResults.Location = new System.Drawing.Point(15, 50);
            this.txtResults.Multiline = true;
            this.txtResults.Name = "txtResults";
            this.txtResults.ReadOnly = true;
            this.txtResults.ScrollBars = System.Windows.Forms.ScrollBars.Both;
            this.txtResults.Size = new System.Drawing.Size(725, 185);
            this.txtResults.TabIndex = 2;
            //
            // lblResultCount
            //
            this.lblResultCount.AutoSize = true;
            this.lblResultCount.Location = new System.Drawing.Point(15, 25);
            this.lblResultCount.Name = "lblResultCount";
            this.lblResultCount.Size = new System.Drawing.Size(77, 12);
            this.lblResultCount.TabIndex = 0;
            this.lblResultCount.Text = "结果数量: 0";
            //
            // btnExport
            //
            this.btnExport.Enabled = false;
            this.btnExport.Location = new System.Drawing.Point(650, 20);
            this.btnExport.Name = "btnExport";
            this.btnExport.Size = new System.Drawing.Size(90, 25);
            this.btnExport.TabIndex = 1;
            this.btnExport.Text = "导出结果";
            this.btnExport.UseVisualStyleBackColor = true;
            this.btnExport.Click += new System.EventHandler(this.btnExport_Click);
            //
            // openFileDialog
            //
            this.openFileDialog.Filter = "文本文件|*.txt|所有文件|*.*";
            this.openFileDialog.Title = "选择数据文件";
            //
            // saveFileDialog
            //
            this.saveFileDialog.Filter = "文本文件|*.txt|所有文件|*.*";
            this.saveFileDialog.Title = "保存结果文件";
            //
            // Form1
            //
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(784, 592);
            this.Controls.Add(this.groupBoxResults);
            this.Controls.Add(this.groupBoxProcess);
            this.Controls.Add(this.groupBoxSettings);
            this.Controls.Add(this.groupBoxFile);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle;
            this.MaximizeBox = false;
            this.Name = "Form1";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "离子镀膜磁控点分布运算系统";
            this.groupBoxFile.ResumeLayout(false);
            this.groupBoxFile.PerformLayout();
            this.groupBoxSettings.ResumeLayout(false);
            this.groupBoxSettings.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxNumber)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMagneticPoints)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numTwoCombinationMax)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numTwoCombinationMin)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numResultMax)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numResultMin)).EndInit();
            this.groupBoxProcess.ResumeLayout(false);
            this.groupBoxProcess.PerformLayout();
            this.groupBoxResults.ResumeLayout(false);
            this.groupBoxResults.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion
    }
}

