# 离子镀膜磁控点分布运算系统需求文档

## 项目概述
开发一个用于离子镀膜磁控点分布运算的程序系统，通过分析原始数据生成符合条件的磁控点组合。

## 详细需求

### 1. 数据输入格式
- **输入文件**：TXT文档，包含100-500行原始字符串
- **数据格式**：每行为数字序列，用"-"分隔，如：`3-5-7-8-14-17-20-21-25-29-32-33`
- **数据范围**：数字范围1-33（最大值可能在33-37之间，暂定33）
- **数据特点**：
  - 每行数字数量不固定
  - 数字严格按从小到大排序
  - 同一行内数字不重复

**示例数据：**
```
3-5-7-8-14-17-20-21-25-29-32-33
1-3-7-9-11-13-15-17-19-25-27-31
2-3-8-10-14-17-24-26-27-31-32
```

### 2. 数据验证
- **验证规则**：检查每行数据是否按从小到大排序且无重复
- **错误处理**：如发现违规数据（如`1-5-7-9-10-11-11-15-28`），在对应行列报错

### 3. 磁控点定义
- **磁控点组成**：由6个不同数字组成的字符串
- **示例**：`7-9-12-19-25-33`

### 4. 组合生成算法

#### 4.1 二元组合生成
- **目标**：从每行原始字符串中生成所有可能的2个数字组合
- **方法**：C(n,2) 组合算法
- **示例**：
  - 原始字符串：`1-2-3-4-5-6`
  - 生成的2数组合：`1-2, 1-3, 1-4, 1-5, 1-6, 2-3, 2-4, 2-5, 2-6, 3-4, 3-5, 3-6, 4-5, 4-6, 5-6`
  - 总计：15种组合



#### 4.2 频次统计
- **统计方法**：遍历所有生成的2数组合，统计每个组合的出现次数
- **分类存储**：
  - 出现1次的组合 → 集合1
  - 出现2次的组合 → 集合2
  - 出现n次的组合 → 集合n

### 5. 磁控点生成算法

#### 5.1 筛选参与运算的组合
- **筛选条件**：选择出现5-50次的2数组合
- **预期数量**：约300组参与运算
- **示例组合**：
```
1-3
8-9
12-20
9-33
...
```

#### 5.2 六元组合生成
- **固定策略**：选定一个2数组合作为固定部分（如`1-3`）
- **补充策略**：从剩余31个数字中选择4个数字补充
- **组合方法**：C(31,4) 组合算法
- **结果示例**：
  - 固定：`1-3`
  - 补充：从`2,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33`中选4个
  - 生成：`1-2-3-4-5-6`, `1-3-5-9-10-23`, `1-3-9-25-26-29-33`等


### 6. 结果筛选

#### 6.1 频次筛选
- **统计方法**：统计所有生成的6数字符串的出现次数
- **筛选条件**：可调节的频次范围
  - 示例1：10-15次（出现10-15次的组合）
  - 示例2：10-13次（出现10-13次的组合）
  - 示例3：15-15次（仅出现15次的组合）

#### 6.2 无效数据剔除
- **无效数据定义**：未参与运算的2数组合
- **计算方法**：
  - 理论总数：C(33,2) = 33×32/2 = 528种
  - 参与运算：300种（假设）
  - 无效数据：228种
- **剔除策略**：从筛选结果中移除包含任何无效2数组合的6数字符串

### 7. 输出要求
- **输出格式**：TXT文档
- **输出内容**：经过所有筛选条件的最终6数字符串组合

## 系统功能模块

### 模块1：数据导入与验证
- 读取TXT文件
- 验证数据格式
- 错误报告

### 模块2：组合生成引擎
- 2数组合生成
- 频次统计
- 6数组合生成

### 模块3：筛选与过滤
- 频次筛选
- 无效数据剔除

### 模块4：结果输出
- 格式化输出
- 文件保存

## 技术要求
- 支持大数据量处理（100-500行输入）
- 高效的组合算法实现
- 灵活的筛选条件配置
- 清晰的错误提示机制

## 预期性能
- 处理速度：能够在合理时间内完成大量组合计算
- 内存使用：优化内存占用，避免内存溢出
- 准确性：确保组合生成和统计的准确性

## 实现要求
- 能够指定最大数为33-37中的哪个为最大数

- 能够指定磁控点的位数（6或9）

- 使用winform实现，界面美观

- 能够选择频次筛选条件（如5-50次的2数组合）

- 能够选择最终结果的频次范围（如10-15次）